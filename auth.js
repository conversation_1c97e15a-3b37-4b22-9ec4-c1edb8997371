const firebaseConfig = {
  apiKey: "AIzaSyBSYsquVVFqpXwFSEi-o9C3mT8czj-o4rc",
  authDomain: "jsi-e851d.firebaseapp.com",
  projectId: "jsi-e851d",
  storageBucket: "jsi-e851d.firebasestorage.app",
  messagingSenderId: "612158191301",
  appId: "1:612158191301:web:b7293af39b3d704b6522ac",
  measurementId: "G-XMDCJNGNXT",
};

// Khởi tạo Firebase
firebase.initializeApp(firebaseConfig);
const auth = firebase.auth();

// L<PERSON>y các phần tử DOM
const messageEl = document.getElementById("message");

// Form đăng nhập
const loginForm = document.getElementById("login-form");
if (loginForm) {
  loginForm.addEventListener("submit", handleLogin);
}

// Form đăng ký
const signupForm = document.getElementById("signup-form");
if (signupForm) {
  signupForm.addEventListener("submit", handleSignup);
}

// Nút đăng nhập Google
const googleBtn = document.getElementById("google-signin-btn");
if (googleBtn) {
  googleBtn.addEventListener("click", handleGoogleLogin);
}

// Hàm hiển thị thông báo
function showMessage(text, isError = false) {
  if (!messageEl) return;
  messageEl.textContent = text;
  messageEl.className = isError ? "message error" : "message success";
}

// Hàm hiển thị thông tin người dùng
function showUserInfo(user) {
  // Tạo hoặc cập nhật phần hiển thị thông tin người dùng
  let userInfoEl = document.getElementById("user-info");

  if (!userInfoEl) {
    // Tạo element mới nếu chưa có
    userInfoEl = document.createElement("div");
    userInfoEl.id = "user-info";
    userInfoEl.className = "user-info";

    // Thêm vào sau form hoặc trước message
    const formBox = document.querySelector(".form-box");
    const messageElement = document.getElementById("message");
    if (formBox && messageElement) {
      formBox.insertBefore(userInfoEl, messageElement);
    }
  }

  // Hiển thị thông tin người dùng
  userInfoEl.innerHTML = `
    <div class="user-profile">
      <h3>Thông tin người dùng</h3>
      <p><strong>Email:</strong> ${user.email}</p>
      ${
        user.displayName
          ? `<p><strong>Tên:</strong> ${user.displayName}</p>`
          : ""
      }
      ${
        user.photoURL
          ? `<img src="${user.photoURL}" alt="Avatar" class="user-avatar">`
          : ""
      }
      <button type="button" id="logout-btn" class="btn btn-secondary">Đăng xuất</button>
    </div>
  `;

  // Ẩn form đăng nhập
  const loginForm = document.getElementById("login-form");
  const googleBtn = document.getElementById("google-signin-btn");
  const switchAuth = document.querySelector(".switch-auth");

  if (loginForm) loginForm.style.display = "none";
  if (googleBtn) googleBtn.style.display = "none";
  if (switchAuth) switchAuth.style.display = "none";

  // Thêm sự kiện đăng xuất
  const logoutBtn = document.getElementById("logout-btn");
  if (logoutBtn) {
    logoutBtn.addEventListener("click", handleLogout);
  }
}

// Hàm xử lý đăng xuất
function handleLogout() {
  auth
    .signOut()
    .then(() => {
      console.log("Đăng xuất thành công");

      // Ẩn thông tin người dùng
      const userInfoEl = document.getElementById("user-info");
      if (userInfoEl) {
        userInfoEl.style.display = "none";
      }

      // Hiển thị lại form đăng nhập
      const loginForm = document.getElementById("login-form");
      const googleBtn = document.getElementById("google-signin-btn");
      const switchAuth = document.querySelector(".switch-auth");

      if (loginForm) loginForm.style.display = "block";
      if (googleBtn) googleBtn.style.display = "block";
      if (switchAuth) switchAuth.style.display = "block";

      showMessage("Đã đăng xuất thành công.");
    })
    .catch((error) => {
      console.error("Lỗi đăng xuất:", error);
      showMessage("Có lỗi xảy ra khi đăng xuất.", true);
    });
}

// Bước 3: Soát lỗi tại client
function validatePassword(password) {
  // b. Password có ít nhất 6 ký tự, tối thiểu 1 ký tự viết hoa, thường, 1 số.
  if (password.length < 6) {
    return "Mật khẩu phải có ít nhất 6 ký tự.";
  }
  if (!/[a-z]/.test(password)) {
    return "Mật khẩu phải chứa ít nhất một ký tự viết thường.";
  }
  if (!/[A-Z]/.test(password)) {
    return "Mật khẩu phải chứa ít nhất một ký tự viết HOA.";
  }
  if (!/[0-9]/.test(password)) {
    return "Mật khẩu phải chứa ít nhất một chữ số.";
  }
  return null; // Hợp lệ
}

// Bước 4: Xử lý đăng ký
async function handleSignup(event) {
  event.preventDefault(); // Ngăn form tự gửi đi

  const email = document.getElementById("signup-email").value;
  const password = document.getElementById("signup-password").value;
  const confirmPassword = document.getElementById("confirm-password").value;

  // a. Email phải có ký tự ‘@’
  if (!email.includes("@")) {
    return showMessage("Email không hợp lệ, phải chứa ký tự '@'.", true);
  }

  // Kiểm tra mật khẩu có khớp không
  if (password !== confirmPassword) {
    return showMessage("Mật khẩu xác nhận không khớp.", true);
  }

  // Kiểm tra độ phức tạp của mật khẩu
  const passwordError = validatePassword(password);
  if (passwordError) {
    return showMessage(passwordError, true);
  }

  try {
    const userCredential = await auth.createUserWithEmailAndPassword(
      email,
      password
    );
    console.log("Đăng ký thành công:", userCredential.user);
    showMessage("Đăng ký thành công! Đang chuyển hướng đến trang đăng nhập...");
    // Tự động chuyển hướng sau khi đăng ký thành công
    setTimeout(() => {
      window.location.href = "login.html";
    }, 2000);
  } catch (error) {
    console.error("Lỗi đăng ký:", error.code, error.message);
    if (error.code === "auth/email-already-in-use") {
      showMessage("Email này đã được sử dụng.", true);
    } else {
      showMessage("Đã có lỗi xảy ra trong quá trình đăng ký.", true);
    }
  }
}

// Bước 4: Xử lý đăng nhập
async function handleLogin(event) {
  event.preventDefault();

  const email = document.getElementById("login-email").value;
  const password = document.getElementById("login-password").value;

  // Kiểm tra đầu vào cơ bản
  if (!email || !password) {
    return showMessage("Vui lòng nhập đầy đủ email và mật khẩu.", true);
  }

  // Kiểm tra email có ký tự '@'
  if (!email.includes("@")) {
    return showMessage("Email không hợp lệ, phải chứa ký tự '@'.", true);
  }

  try {
    const userCredential = await auth.signInWithEmailAndPassword(
      email,
      password
    );
    const user = userCredential.user;
    console.log("Đăng nhập thành công:", user);

    // Hiển thị thông tin người dùng
    showUserInfo(user);
    showMessage(`Đăng nhập thành công! Chào mừng ${user.email}`);

    // Ở đây bạn có thể chuyển hướng người dùng đến trang chính
    // ví dụ: window.location.href = '/dashboard.html';
  } catch (error) {
    console.error("Lỗi đăng nhập:", error.code, error.message);

    // Xử lý các loại lỗi cụ thể
    let errorMessage = "Đã có lỗi xảy ra trong quá trình đăng nhập.";

    switch (error.code) {
      case "auth/user-not-found":
        errorMessage = "Bạn chưa có tài khoản.";
        break;
      case "auth/wrong-password":
        errorMessage = "Mật khẩu không chính xác.";
        break;
      case "auth/invalid-credential":
      case "auth/invalid-login-credentials":
        errorMessage = "Bạn chưa có tài khoản.";
        break;
      case "auth/too-many-requests":
        errorMessage = "Quá nhiều lần thử đăng nhập. Vui lòng thử lại sau.";
        break;
      case "auth/user-disabled":
        errorMessage = "Tài khoản này đã bị vô hiệu hóa.";
        break;
      case "auth/internal-error":
        errorMessage = "Lỗi hệ thống. Vui lòng thử lại sau.";
        break;
      default:
        errorMessage = "Bạn chưa có tài khoản.";
    }

    showMessage(errorMessage, true);
  }
}

// Xử lý đăng nhập bằng Google
async function handleGoogleLogin() {
  const provider = new firebase.auth.GoogleAuthProvider();

  try {
    const result = await auth.signInWithPopup(provider);
    const user = result.user;
    console.log("Đăng nhập bằng Google thành công:", user);

    // Hiển thị thông tin người dùng
    showUserInfo(user);
    showMessage(`Chào mừng ${user.displayName || user.email}!`);

    // Chuyển hướng đến trang chính
    // ví dụ: window.location.href = '/dashboard.html';
  } catch (error) {
    console.error("Lỗi đăng nhập Google:", error);
    if (error.code === "auth/popup-closed-by-user") {
      showMessage("Cửa sổ đăng nhập Google đã bị đóng.", true);
    } else {
      showMessage("Không thể đăng nhập bằng Google.", true);
    }
  }
}
