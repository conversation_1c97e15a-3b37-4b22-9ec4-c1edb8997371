const firebaseConfig = {
  apiKey: "AIzaSyBSYsquVVFqpXwFSEi-o9C3mT8czj-o4rc",
  authDomain: "jsi-e851d.firebaseapp.com",
  projectId: "jsi-e851d",
  storageBucket: "jsi-e851d.firebasestorage.app",
  messagingSenderId: "612158191301",
  appId: "1:612158191301:web:b7293af39b3d704b6522ac",
  measurementId: "G-XMDCJNGNXT",
};

firebase.initializeApp(firebaseConfig);
const auth = firebase.auth();
const messageEl = document.getElementById("message");

document.getElementById("login-form")?.addEventListener("submit", handleLogin);
document
  .getElementById("signup-form")
  ?.addEventListener("submit", handleSignup);
document
  .getElementById("google-signin-btn")
  ?.addEventListener("click", handleGoogleLogin);

function showMessage(text, isError = false) {
  if (!messageEl) return;
  messageEl.textContent = text;
  messageEl.className = isError ? "message error" : "message success";
}

function showUserInfo(user) {
  let userInfoEl = document.getElementById("user-info");
  if (!userInfoEl) {
    userInfoEl = document.createElement("div");
    userInfoEl.id = "user-info";
    userInfoEl.className = "user-info";
    document.querySelector(".form-box").insertBefore(userInfoEl, messageEl);
  }
  userInfoEl.innerHTML = `
    <div class="user-profile">
      <h3>Thông tin người dùng</h3>
      <p><strong>Email:</strong> ${user.email}</p>
      ${
        user.displayName
          ? `<p><strong>Tên:</strong> ${user.displayName}</p>`
          : ""
      }
      ${
        user.photoURL
          ? `<img src="${user.photoURL}" alt="Avatar" class="user-avatar">`
          : ""
      }
      <button type="button" onclick="handleLogout()" class="btn btn-secondary">Đăng xuất</button>
    </div>`;
  document.getElementById("login-form").style.display = "none";
  document.getElementById("google-signin-btn").style.display = "none";
  document.querySelector(".switch-auth").style.display = "none";
}

function handleLogout() {
  auth.signOut().then(() => {
    document.getElementById("user-info").style.display = "none";
    document.getElementById("login-form").style.display = "block";
    document.getElementById("google-signin-btn").style.display = "block";
    document.querySelector(".switch-auth").style.display = "block";
    showMessage("Đã đăng xuất thành công.");
  });
}

function validatePassword(password) {
  if (password.length < 6) return "Mật khẩu phải có ít nhất 6 ký tự.";
  if (!/[a-z]/.test(password))
    return "Mật khẩu phải chứa ít nhất một ký tự viết thường.";
  if (!/[A-Z]/.test(password))
    return "Mật khẩu phải chứa ít nhất một ký tự viết HOA.";
  if (!/[0-9]/.test(password)) return "Mật khẩu phải chứa ít nhất một chữ số.";
  return null;
}

async function handleSignup(event) {
  event.preventDefault();
  const email = document.getElementById("signup-email").value;
  const password = document.getElementById("signup-password").value;
  const confirmPassword = document.getElementById("confirm-password").value;

  if (!email.includes("@"))
    return showMessage("Email không hợp lệ, phải chứa ký tự '@'.", true);
  if (password !== confirmPassword)
    return showMessage("Mật khẩu xác nhận không khớp.", true);

  const passwordError = validatePassword(password);
  if (passwordError) return showMessage(passwordError, true);

  try {
    await auth.createUserWithEmailAndPassword(email, password);
    showMessage("Đăng ký thành công! Đang chuyển hướng đến trang đăng nhập...");
    setTimeout(() => (window.location.href = "login.html"), 2000);
  } catch (error) {
    const message =
      error.code === "auth/email-already-in-use"
        ? "Email này đã được sử dụng."
        : "Đã có lỗi xảy ra trong quá trình đăng ký.";
    showMessage(message, true);
  }
}

async function handleLogin(event) {
  event.preventDefault();
  const email = document.getElementById("login-email").value;
  const password = document.getElementById("login-password").value;

  if (!email || !password)
    return showMessage("Vui lòng nhập đầy đủ email và mật khẩu.", true);
  if (!email.includes("@"))
    return showMessage("Email không hợp lệ, phải chứa ký tự '@'.", true);

  try {
    const userCredential = await auth.signInWithEmailAndPassword(
      email,
      password
    );
    showUserInfo(userCredential.user);
    showMessage(`Đăng nhập thành công! Chào mừng ${userCredential.user.email}`);
  } catch (error) {
    showMessage(
      "Bạn nhập sai email hoặc mật khẩu hoặc bạn chưa có tài khoản",
      true
    );
  }
}

async function handleGoogleLogin() {
  try {
    const result = await auth.signInWithPopup(
      new firebase.auth.GoogleAuthProvider()
    );
    showUserInfo(result.user);
    showMessage(`Chào mừng ${result.user.displayName || result.user.email}!`);
  } catch (error) {
    const message =
      error.code === "auth/popup-closed-by-user"
        ? "Cửa sổ đăng nhập Google đã bị đóng."
        : "Không thể đăng nhập bằng Google.";
    showMessage(message, true);
  }
}
