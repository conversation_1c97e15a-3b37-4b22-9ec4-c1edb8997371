// Bước 2: Kết nối Firebase vào dự án (thay thế bằng cấu hình của bạn)
const firebaseConfig = {
  apiKey: "YOUR_API_KEY", // <--- THAY THẾ BẰNG DỮ LIỆU CỦA BẠN
  authDomain: "YOUR_AUTH_DOMAIN", // <--- THAY THẾ BẰNG DỮ LIỆU CỦA BẠN
  projectId: "YOUR_PROJECT_ID", // <--- THAY THẾ BẰNG DỮ LIỆU CỦA BẠN
  storageBucket: "YOUR_STORAGE_BUCKET", // <--- THAY THẾ BẰNG DỮ LIỆU CỦA BẠN
  messagingSenderId: "YOUR_MESSAGING_SENDER_ID", // <--- THAY THẾ BẰNG DỮ LIỆU CỦA BẠN
  appId: "YOUR_APP_ID", // <--- THAY THẾ BẰNG DỮ LIỆU CỦA BẠN
};

// Khởi tạo Firebase
firebase.initializeApp(firebaseConfig);
const auth = firebase.auth();

// Lấy các phần tử DOM
const emailInput = document.getElementById("email");
const passwordInput = document.getElementById("password");
const loginBtn = document.getElementById("loginBtn");
const registerBtn = document.getElementById("registerBtn");
const messageEl = document.getElementById("message");

// Hàm hiển thị thông báo
function showMessage(message, isError = false) {
  messageEl.textContent = message;
  messageEl.className = isError ? "error" : "success";
}

// Bước 3: Soát lỗi tại client
function validateInput(email, password) {
  // a. Email phải có ký tự ‘@’
  if (!email.includes("@")) {
    return "Email không hợp lệ, phải chứa ký tự '@'.";
  }

  // b. Password có ít nhất 6 ký tự, tối thiểu 1 ký tự viết hoa, thường, 1 số.
  if (password.length < 6) {
    return "Mật khẩu phải có ít nhất 6 ký tự.";
  }
  if (!/[a-z]/.test(password)) {
    return "Mật khẩu phải chứa ít nhất một ký tự viết thường.";
  }
  if (!/[A-Z]/.test(password)) {
    return "Mật khẩu phải chứa ít nhất một ký tự viết HOA.";
  }
  if (!/[0-9]/.test(password)) {
    return "Mật khẩu phải chứa ít nhất một chữ số.";
  }

  return null; // Trả về null nếu không có lỗi
}

// Bước 4: Xử lý đăng ký
registerBtn.addEventListener("click", () => {
  const email = emailInput.value;
  const password = passwordInput.value;

  // Soát lỗi đầu vào trước khi gửi tới Firebase
  const validationError = validateInput(email, password);
  if (validationError) {
    showMessage(validationError, true);
    return; // Dừng thực thi nếu có lỗi
  }

  // Sử dụng Firebase Authentication để tạo người dùng mới
  auth
    .createUserWithEmailAndPassword(email, password)
    .then((userCredential) => {
      // Đăng ký thành công
      const user = userCredential.user;
      console.log("Đăng ký thành công:", user);
      showMessage(`Đăng ký thành công với email: ${user.email}`);
    })
    .catch((error) => {
      // Xử lý lỗi
      console.error("Lỗi đăng ký:", error);
      let errorMessage = "Đã có lỗi xảy ra. Vui lòng thử lại.";
      // Dịch một số lỗi phổ biến của Firebase
      if (error.code === "auth/email-already-in-use") {
        errorMessage = "Email này đã được sử dụng. Vui lòng chọn email khác.";
      } else if (error.code === "auth/weak-password") {
        errorMessage =
          "Mật khẩu quá yếu. Vui lòng chọn mật khẩu khác an toàn hơn.";
      }
      showMessage(errorMessage, true);
    });
});

// Bước 4: Xử lý đăng nhập
loginBtn.addEventListener("click", () => {
  const email = emailInput.value;
  const password = passwordInput.value;

  // Kiểm tra đầu vào cơ bản
  if (!email || !password) {
    showMessage("Vui lòng nhập cả email và mật khẩu.", true);
    return;
  }

  // Sử dụng Firebase Authentication để đăng nhập
  auth
    .signInWithEmailAndPassword(email, password)
    .then((userCredential) => {
      // Đăng nhập thành công
      const user = userCredential.user;
      console.log("Đăng nhập thành công:", user);
      showMessage(`Đăng nhập thành công! Chào mừng ${user.email}`);
    })
    .catch((error) => {
      // Xử lý lỗi
      console.error("Lỗi đăng nhập:", error);
      let errorMessage = "Thông tin đăng nhập không chính xác.";
      // Dịch lỗi của Firebase
      if (
        error.code === "auth/user-not-found" ||
        error.code === "auth/wrong-password" ||
        error.code === "auth/invalid-credential"
      ) {
        errorMessage = "Email hoặc mật khẩu không đúng. Vui lòng kiểm tra lại.";
      }
      showMessage(errorMessage, true);
    });
});
