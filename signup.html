<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON><PERSON><PERSON> ký</title>
    <link rel="stylesheet" href="style.css" />
  </head>
  <body>
    <div class="auth-container">
      <div class="form-box">
        <h1>Signup</h1>

        <p id="message" class="message"></p>

        <form id="signup-form">
          <div class="input-group">
            <label for="signup-email">Email</label>
            <input type="email" id="signup-email" required />
          </div>

          <div class="input-group">
            <label for="signup-password">Create password</label>
            <input type="password" id="signup-password" required />
          </div>

          <div class="input-group">
            <label for="confirm-password">Confirm password</label>
            <input type="password" id="confirm-password" required />
          </div>

          <button type="submit" class="btn btn-primary">Signup</button>
        </form>

        <div class="divider"><span>Or</span></div>

        <button type="button" id="google-signin-btn" class="btn btn-social">
          <img
            src="https://www.gstatic.com/firebasejs/ui/2.0.0/images/auth/google.svg"
            alt="Google Logo"
          />
          <span>Login with Google</span>
        </button>

        <p class="switch-auth">
          Already have an account? <a href="login.html">Login</a>
        </p>
      </div>
    </div>

    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-auth.js"></script>

    <script src="./auth_compact.js"></script>
  </body>
</html>
