@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap");

:root {
  --primary-color: #4a6dff;
  --primary-hover-color: #3b57cc;
  --secondary-color: #334d6e;
  --background-color: #e6e9f2;
  --form-background: #ffffff;
  --input-background: #f7f8fa;
  --text-color: #6a7a90;
  --border-color: #d9d9d9;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Poppins", sans-serif;
  background-color: var(--background-color);
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
}

.auth-container {
  width: 100%;
  max-width: 420px;
}

.form-box {
  background-color: var(--form-background);
  padding: 40px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.form-box h1 {
  color: var(--secondary-color);
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 30px;
}

.input-group {
  position: relative;
  margin-bottom: 20px;
  text-align: left;
}

.input-group label {
  display: block;
  color: var(--secondary-color);
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

.input-group input {
  width: 100%;
  padding: 14px;
  background-color: var(--input-background);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 16px;
  color: var(--secondary-color);
  transition: border-color 0.3s;
}

.input-group input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.toggle-password {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: var(--text-color);
}
/* Di chuyển icon lên trên khi có label */
.input-group label + input + .toggle-password {
  top: 60%;
}

.options {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
}

.forgot-password {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
}

.forgot-password:hover {
  text-decoration: underline;
}

.btn {
  width: 100%;
  padding: 15px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-hover-color);
}

.divider {
  color: var(--text-color);
  margin: 30px 0;
  display: flex;
  align-items: center;
  text-align: center;
}

.divider::before,
.divider::after {
  content: "";
  flex: 1;
  border-bottom: 1px solid var(--border-color);
}

.divider:not(:empty)::before {
  margin-right: 0.5em;
}

.divider:not(:empty)::after {
  margin-left: 0.5em;
}

.btn-social {
  background-color: #f7f8fa;
  color: var(--secondary-color);
  border: 1px solid var(--border-color);
}

.btn-social:hover {
  background-color: #eff1f4;
}

.btn-social i {
  color: #1877f2; /* Facebook blue */
  font-size: 20px;
}

.switch-auth {
  margin-top: 30px;
  color: var(--text-color);
  font-size: 14px;
}

.switch-auth a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 600;
}

.switch-auth a:hover {
  text-decoration: underline;
}
/* Thêm các đoạn này vào cuối file style.css của bạn */

/* --- Styling cho nút Google --- */
.btn-social img {
  width: 20px;
  height: 20px;
}

/* --- Styling cho khu vực thông báo --- */
.message {
  padding: 10px;
  margin-bottom: 20px;
  border-radius: 8px;
  text-align: center;
  font-weight: 500;
  display: none; /* Mặc định ẩn đi */
}

.message.success {
  background-color: #d4edda;
  color: #155724;
  display: block; /* Hiện ra khi có class success */
}

.message.error {
  background-color: #f8d7da;
  color: #721c24;
  display: block; /* Hiện ra khi có class error */
}

/* Bỏ icon con mắt đi vì chưa có JS xử lý */
.toggle-password {
  display: none;
}

/* User Info Styles */
.user-info {
  margin-bottom: 20px;
  padding: 20px;
  background-color: var(--input-background);
  border-radius: 12px;
  border: 1px solid var(--border-color);
}

.user-profile h3 {
  color: var(--secondary-color);
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 15px;
}

.user-profile p {
  color: var(--text-color);
  font-size: 14px;
  margin-bottom: 8px;
  text-align: left;
}

.user-profile strong {
  color: var(--secondary-color);
  font-weight: 600;
}

.user-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  margin: 10px 0;
  border: 2px solid var(--primary-color);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
  margin-top: 15px;
}

.btn-secondary:hover {
  background-color: #2a3f5a;
}
